# 🏥 医学影像诊断系统

## 📋 项目简介

云南省毒品依赖戒治技术创新中心开发的医学影像诊断系统，基于FastAPI和MySQL开发。

## 🔧 技术栈

- **Python 3.11+** - 编程语言
- **FastAPI** - 高性能Web框架
- **SQLAlchemy** - 强大的ORM工具
- **MySQL** - 关系型数据库
- **Alembic** - 数据库迁移工具
- **Pydantic** - 数据验证库
- **Jose** - JWT认证实现

## 💻 开发环境设置

### 1. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env.dev

# 编辑.env.dev文件，输入必要的参数及密钥
```

### 2. 虚拟环境与依赖安装
```bash
# 创建虚拟环境
uv venv

# 安装依赖
uv sync
```

### 3. 启动开发服务器
```bash
# 以开发模式启动项目
python run.py dev
```

> **注意**: 开发模式下数据库不会自动启动，请确保数据库已启动，或使用以下命令启动:
> ```bash
> docker compose up mysql_db -d
> ```

## 🚀 生产环境设置

```bash
# 复制并配置生产环境变量
cp .env.example .env.prod

# 编辑.env.prod文件，输入必要的参数及密钥

# 启动生产服务
python run.py prod
```

> **提示**: 生产环境已通过Docker打包好所有必要的环境配置。

## 📄 许可证

版权所有 © 2024 云南省毒品依赖戒治技术创新中心