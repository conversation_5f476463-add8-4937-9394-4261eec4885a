from fpdf import FPDF
import pandas as pd
from typing import Dict, Optional
import os
from io import StringIO
from pathlib import Path
import oss2
import warnings

from app.schemas.report import ReportContext, PDFConfig, HeartReportData
from app.core.logger import get_logger
from app.core.config import settings
from app.core.config import BASE_DIR

# 部位名称的中英文对照字典
REGION_TRANSLATIONS = {
    "head": "头部",
    "brain": "脑部",
    "abdomen": "腹部",
    "chest": "胸部",
    "pelvis": "盆腔",
    "spine": "脊椎",
    "cspine": "颈椎",
    "lspine": "腰椎",
    "tspine": "胸椎",
    "knee": "膝关节",
    "shoulder": "肩关节",
    "hip": "髋关节",
    "elbow": "肘关节",
    "wrist": "腕关节",
    "ankle": "踝关节",
    "foot": "足部",
    "hand": "手部",
    "neck": "颈部",
    "joint": "关节",
    "whole_body": "全身",
    "cardiac": "心脏",
    "vascular": "血管",
    "lymph": "淋巴",
    "liver": "肝脏",
    "kidney": "肾脏",
    "bowel": "肠道",
    "stomach": "胃部",
    "pancreas": "胰腺",
    "spleen": "脾脏"
}


class DiagnosisReportPDF(FPDF):
    """诊断报告PDF生成器"""
    
    def __init__(self, context: ReportContext, config: PDFConfig):
        super().__init__()
        self.context = context
        self.config = config
        self.logger = get_logger("pdf_generator")
        warnings.filterwarnings('ignore', message='cmap value.*', category=UserWarning)

        # 添加字体
        self._add_fonts()
        
    def _add_fonts(self):
        """添加所需字体"""
        try:
            # 使用预定义的字体文件路径
            regular_font = str(BASE_DIR / 'static' / 'fonts' / 'SourceHanSansCN-Regular.ttf')
            heavy_font = str(BASE_DIR / 'static' / 'fonts' / 'SourceHanSansCN-Heavy.ttf')
            print(f"DEBUG: Calculated font path inside container is: {heavy_font}")

            # 添加字体
            self.add_font('source_han', '', regular_font, uni=True)
            self.add_font('source_han', 'I', regular_font, uni=True)
            self.add_font('source_han', 'B', heavy_font, uni=True)
            self.add_font('source_han', 'Heavy', heavy_font, uni=True)
        except Exception as e:
            self.logger.error(f"添加字体失败: {str(e)}")
            raise
    
    def header(self):
        """生成页眉"""
        try:
            # 设置背景图片
            template_path = str(BASE_DIR / 'static' / 'template.jpg')
            self.image(template_path, 0, 0, 210)
            
            # 设置页面边距
            self.set_left_margin(self.config.margin)
            self.set_right_margin(self.config.margin)
            self.set_x(self.config.margin)
            
            # 添加标题
            self.ln(self.config.title_margin_top)
            self.set_font('source_han', 'Heavy', self.config.title_font_size)
            self.set_text_color(0, 0, 0)
            self.cell(0, self.config.line_height, "医学影像学报告", 0, 1, 'C')
            self.ln(2)
            
            # 添加日期
            self.set_font('source_han', '', self.config.normal_font_size)
            self.cell(0, 5, f"检查日期: {self.context.check_date.strftime('%Y年%m月%d日')}", 0, 1, 'R')
        except Exception as e:
            self.logger.error(f"生成页眉失败: {str(e)}")
            raise
    
    def footer(self):
        """生成页脚"""
        self.set_y(-self.config.footer_margin)
        self.set_font('source_han', 'I', 8)
        self.cell(0, 10, f'第 {self.page_no()} 页', 0, 0, 'C')
    
    def add_patient_info(self):
        """添加患者基本信息"""
        try:
            # 添加患者ID
            self.set_font('source_han', 'B', self.config.normal_font_size)
            self.cell(0, self.config.line_height, f"ID: {self.context.subject_id}", 0, 1, 'L')
            
            # 基本信息标题
            self.set_fill_color(240, 240, 240)
            self.cell(0, self.config.line_height, "基本信息", 1, 1, 'L', 1)
            
            # 设置信息表格的列宽
            label_width = value_width = 20
            
            # 添加基本信息表格
            info_items = [
                ("姓名", self.context.name),
                ("性别", self.context.gender),
                ("年龄", f"{self.context.age}岁"),
                ("检查日期", self.context.check_date.strftime("%Y-%m-%d"))
            ]
            
            for label, value in info_items:
                self.set_font('source_han', 'B', self.config.normal_font_size)
                self.cell(label_width, self.config.line_height, label, 1, 0, 'C')
                self.set_font('source_han', '', self.config.normal_font_size)
                self.cell(value_width, self.config.line_height, value, 1, 0, 'C')
            
            self.ln()
        except Exception as e:
            self.logger.error(f"添加患者信息失败: {str(e)}")
            raise
    
    def add_heart_report(self, heart_data: Optional[HeartReportData]):
        """添加心脏报告数据"""
        if not heart_data:
            return
            
        try:
            self.ln(2)
            
            # 添加标题
            self.set_fill_color(235, 245, 255)
            self.set_font('source_han', 'B', self.config.normal_font_size)
            self.cell(0, self.config.line_height, "心功能", 1, 1, 'L', 1)
            
            # 设置表格样式
            self.set_font('source_han', '', self.config.normal_font_size)
            
            # 添加表头
            self.set_fill_color(230, 230, 230)
            for i, header in enumerate(heart_data.headers):
                self.cell(heart_data.column_widths[i], self.config.line_height, 
                         str(header), 1, 0, 'C', 1)
            self.ln()
            
            # 添加数据行
            fill = False
            for row in heart_data.data:
                for i, value in enumerate(row):
                    self.cell(heart_data.column_widths[i], self.config.line_height,
                            str(value), 1, 0, 'C', fill)
                self.ln()
                fill = not fill
        except Exception as e:
            self.logger.error(f"添加心脏报告失败: {str(e)}")
            raise
    
    def add_diagnosis_results(self):
        """添加诊断结果"""
        try:
            self.ln(2)
            
            for i, result in enumerate(self.context.diagnosis_results):
                if i > 0:
                    self.ln(2)
                
                # 获取部位的中文名称
                region_chinese = REGION_TRANSLATIONS.get(result.region.lower(), result.region)
                
                # 添加部位标题
                self.set_fill_color(235, 245, 255)
                self.set_font('source_han', 'B', self.config.normal_font_size)
                self.cell(0, self.config.line_height, f"{region_chinese}", 1, 1, 'L', 1)
                
                # 添加诊断内容
                self.set_font('source_han', '', self.config.normal_font_size)
                self.multi_cell(0, self.config.line_height, result.result, 1)
        except Exception as e:
            self.logger.error(f"添加诊断结果失败: {str(e)}")
            raise


def create_report_pdf(output_path: str, context: ReportContext) -> None:
    """
    创建诊断报告PDF
    
    参数:
        output_path: PDF输出路径
        context: 报告上下文数据
    """
    logger = get_logger("pdf_generator")
    
    try:
        # 创建PDF配置
        config = PDFConfig()
        
        # 创建PDF实例
        pdf = DiagnosisReportPDF(context, config)
        
        # 获取心脏报告数据
        heart_data = _get_heart_report_data(context.subject_id)
        
        # 生成报告
        pdf.add_page()
        pdf.set_auto_page_break(auto=True, margin=20)
        
        # 添加报告内容
        pdf.add_patient_info()
        pdf.add_diagnosis_results()
        if heart_data:
            pdf.add_heart_report(heart_data)
        
        # 保存PDF
        pdf.output(output_path)
        logger.info(f"成功生成报告: {output_path}")
        
    except Exception as e:
        logger.error(f"生成PDF报告失败: {str(e)}")
        raise


def _get_heart_report_data(subject_id: str) -> Optional[HeartReportData]:
    """
    从OSS获取心脏报告数据
    
    参数:
        subject_id: 受试者ID
    
    返回:
        HeartReportData 或 None（如果没有数据）
    """
    logger = get_logger("pdf_generator")
    
    try:
        # 初始化OSS客户端
        auth = oss2.Auth(settings.OSS_ACCESS_KEY_ID, settings.OSS_ACCESS_KEY_SECRET)
        bucket = oss2.Bucket(auth, settings.OSS_ENDPOINT, settings.OSS_BUCKET_NAME)
        
        # 尝试从OSS获取心脏报告数据
        heart_report_key = f"heart_report/{subject_id}.csv"
        
        try:
            # 下载CSV文件内容
            csv_object = bucket.get_object(heart_report_key)
            csv_content = csv_object.read()
            
            # 尝试不同编码解码CSV内容
            for encoding in ['gbk', 'gb2312']:
                try:
                    csv_text = csv_content.decode(encoding)
                    df = pd.read_csv(StringIO(csv_text))
                    
                    return HeartReportData(
                        headers=df.columns.tolist(),
                        data=df.values.tolist()
                    )
                except UnicodeDecodeError:
                    continue
            
            logger.error(f"无法解码CSV文件，尝试的编码：GBK, GB2312")
            return None
            
        except Exception as e:
            logger.warning(f"获取心脏报告数据失败: {str(e)}")
            return None
            
    except Exception as e:
        logger.error(f"处理心脏报告数据失败: {str(e)}")
        return None 