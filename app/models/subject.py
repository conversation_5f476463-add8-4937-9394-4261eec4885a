from sqlalchemy import Column, Integer, String, Date, Bo<PERSON>an, Enum
from sqlalchemy.orm import relationship, foreign
from app.db.session import Base

# 枚举定义
GenderEnum = ('M', 'F')
DiagnosisStatusEnum = ('pending', 'draft', 'submitted')


class SubjectInfo(Base):
    """受试者信息表模型"""
    __tablename__ = "subjects_info"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    subject_id = Column(String(30), index=True) 
    check_date = Column(Date, nullable=False)
    name = Column(String(100), nullable=False)
    pinyin = Column(String(100))
    age = Column(Integer, nullable=False)
    gender = Column(Enum(*GenderEnum), nullable=False)
    remark = Column(String(255))

    checks = relationship(
        "SubjectCheck",
        primaryjoin="SubjectInfo.subject_id == foreign(SubjectCheck.subject_id)",
        back_populates="subject",
        viewonly=True  # 只读，不级联写入
    )


class SubjectCheck(Base):
    """受试者的部位检查记录"""
    __tablename__ = "subject_checks"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    subject_id = Column(String(30), index=True)  # 没有 foreign key
    region = Column(String(50), nullable=False)  # 如 'head', 'abdomen'

    subject = relationship(
        "SubjectInfo",
        primaryjoin="foreign(SubjectCheck.subject_id) == SubjectInfo.subject_id",
        back_populates="checks",
        viewonly=True
    )
