from sqlalchemy import Column, Integer, String, Boolean
from app.db.session import Base


class User(Base):
    """用户表模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="用户ID")
    username = Column(String(20), unique=True, nullable=False, comment="用户名")
    password = Column(String(256), nullable=False, comment="密码哈希")
    name = Column(String(20), nullable=False, comment="真实姓名")
    is_disabled = Column(Boolean, nullable=False, default=False, comment="是否禁用")
    role = Column(String(50), nullable=False, default="user", comment="角色权限")
