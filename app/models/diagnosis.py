from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey
from sqlalchemy.orm import relationship, foreign

from app.db.session import Base


class DiagnosisResult(Base):
    """诊断结果表模型"""
    __tablename__ = "diagnosis_results"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    subject_id = Column(String(30), index=True, nullable=False)
    name = Column(String(100), nullable=False)
    check_date = Column(Date, nullable=False)
    region = Column(String(100), nullable=False)
    submit_time = Column(DateTime, nullable=False)
    submit_user = Column(String(20), index=True, nullable=False) 
    result = Column(String(255), nullable=False)
    status = Column(String(50), nullable=False, default="pending") # 'pending', 'draft', 'submitted'
    
    # 关联受试者 - 不使用外键但保持关系
    subject = relationship(
        "SubjectInfo",
        primaryjoin="foreign(DiagnosisResult.subject_id) == SubjectInfo.subject_id",
        viewonly=True
    )
    
    # 关联用户 - 不使用外键但保持关系
    user = relationship(
        "User", 
        primaryjoin="foreign(DiagnosisResult.submit_user) == User.username",
        viewonly=True
    ) 