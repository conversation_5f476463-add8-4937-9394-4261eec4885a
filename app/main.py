from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pathlib import Path
import logging
import time
from logging.handlers import RotatingFileHandler
import os

from app.api.api import api_router
from app.core.config import settings
from app.core.logger import app_logger, setup_logger


app = FastAPI(title=settings.PROJECT_NAME, debug=settings.DEBUG)

# Set all CORS enabled origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

@app.get("/")
def read_root():
    app_logger.info('访问了首页')
    return {"message": "欢迎使用医学影像诊断系统 API", "env": settings.ENV}


@app.get("/health")
def health_check():
    app_logger.debug("健康检查被调用")
    return {"status": "ok", "env": settings.ENV} 