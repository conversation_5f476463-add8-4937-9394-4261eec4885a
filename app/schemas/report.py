from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import date
from dataclasses import dataclass


class ReportParams(BaseModel):
    """报告查询参数"""
    start_date: date = Field(..., description="开始日期")
    end_date: date = Field(..., description="结束日期")


class Report(BaseModel):
    """报告信息"""
    subject_id: str
    name: str
    regions: List[str]
    status: int
    status_text: str
    check_date: str


class ReportResponse(BaseModel):
    """报告列表响应"""
    reports: List[Report]


class BatchDownloadRequest(BaseModel):
    """批量下载请求模型"""
    subject_ids: List[str]


class StatsResponse(BaseModel):
    """统计数据响应模型"""
    totalPatients: int
    diagnosedCount: int
    totalDiagnosisCount: int
    regionCounts: Dict[str, int]


class ReportContext(BaseModel):
    """报告上下文数据模型"""
    subject_id: str
    name: str
    gender: str
    age: int
    check_date: date
    diagnosis_results: List[Any]  # 这里使用Any因为诊断结果的具体结构在原代码中是从数据库查询得到的 


class PDFConfig(BaseModel):
    """PDF生成配置"""
    margin: int = Field(default=25, description="页面边距(mm)")
    title_font_size: int = Field(default=18, description="标题字体大小")
    normal_font_size: int = Field(default=9, description="正常字体大小")
    line_height: int = Field(default=8, description="行高")
    title_margin_top: int = Field(default=18, description="标题上边距")
    footer_margin: int = Field(default=16, description="页脚边距")


class HeartReportData(BaseModel):
    """心脏报告数据"""
    column_widths: List[int] = Field(default=[48, 28, 28, 28, 28], description="列宽配置")
    headers: List[str]
    data: List[List[Any]] 