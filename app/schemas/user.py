from typing import Optional

from pydantic import BaseModel, Field


# Shared properties
class UserBase(BaseModel):
    username: str
    name: Optional[str] = None


# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str


# Properties to receive via API on update
class UserUpdate(UserBase):
    password: Optional[str] = None


# Properties for login
class UserLogin(BaseModel):
    username: str
    password: str


# Properties shared by models stored in DB
class UserInDBBase(UserBase):
    class Config:
        from_attributes = True


# Additional properties to return via API
class User(UserInDBBase):
    pass


# Additional properties stored in DB
class UserInDB(UserInDBBase):
    password: str 