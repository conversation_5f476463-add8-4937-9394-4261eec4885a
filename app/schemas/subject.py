from datetime import date
from typing import Optional, Any, Dict, List

from pydantic import BaseModel


class SubjectCheck(BaseModel):
    id: int
    subject_id: str
    region: str
    check_status: Optional[bool] = None
    diagnosis_status: str = "pending"
    
    class Config:
        from_attributes = True


# Properties to return to client
class Subject(BaseModel):
    subject_id: str
    check_date: date
    name: str
    pinyin: str
    age: int
    gender: str
    remark: Optional[str] = None
    region: str
    diagnosis_status: str
    
    class Config:
        from_attributes = True


# For filtering subjects
class SubjectFilter(BaseModel):
    start_date: Optional[date] = None
    end_date: Optional[date] = None 
    region: Optional[str] = None 