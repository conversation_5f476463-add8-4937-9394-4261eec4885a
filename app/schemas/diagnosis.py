from datetime import date, datetime
from typing import Optional, Union

from pydantic import BaseModel


class Diagnosis(BaseModel):
    id: Optional[int] = None
    subject_id: str
    name: Optional[str] = None
    pinyin: Optional[str] = None
    age: Optional[int] = None
    gender: Optional[str] = None
    check_date: date
    region: str
    submit_time: Optional[datetime] = None
    submit_user: Optional[str] = None
    result: str
    status: str = "pending"
    
    class Config:
        from_attributes = True


class DiagnosisCreate(BaseModel):
    """用于创建和更新诊断的模型"""
    subject_id: str
    username: str  # 提交用户名
    result: str    # 诊断结果
    check_date: Union[date, str]  # 检查日期，可以是日期对象或ISO格式字符串
    region: str    # 区域（如head, abdomen等）
    status: str    # 状态：draft（暂存）或submitted（提交）

