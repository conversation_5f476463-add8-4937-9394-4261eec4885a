from datetime import datetime, timedelta
from typing import Any, Optional, Union
import hashlib

from jose import jwt
from passlib.context import Crypt<PERSON>ontext

from app.core.config import settings

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_access_token(
    subject: Union[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    """创建 JWT 访问令牌"""
    if expires_delta:
        expire = datetime.now() + expires_delta
    else:
        expire = datetime.now() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码 - 使用MD5"""
    md5_password = hashlib.md5(plain_password.encode()).hexdigest()
    return md5_password == hashed_password


def get_password_hash(password: str) -> str:
    """获取密码的MD5哈希值"""
    return hashlib.md5(password.encode()).hexdigest()