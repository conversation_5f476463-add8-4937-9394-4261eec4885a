import os
from typing import Any, Dict, Optional
from dotenv import load_dotenv
from pathlib import Path

from pydantic import MySQLDsn, field_validator
from pydantic_settings import BaseSettings

BASE_DIR = Path(__file__).parent.parent.parent

# 获取环境变量，默认为开发环境
ENV = os.getenv("ENV", "dev")

# 根据环境加载对应的.env文件
env_file = f".env.{ENV}"
if not os.path.exists(env_file):
    raise FileNotFoundError(f"环境配置文件 {env_file} 不存在！")
load_dotenv(env_file)

class Settings(BaseSettings):
    """应用程序设置"""
    # 环境配置
    ENV: str = os.getenv("ENV", "dev")  # 环境: dev, prod
    # ENV: str = 'prod'
    DEBUG: bool = os.getenv("DEBUG", "True").lower() in ("true", "1", "t")
    ALLOWED_HOSTS: list = os.getenv("ALLOWED_HOSTS", "*").split(",")
    
    PROJECT_NAME: str = "医学影像诊断系统"
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key")
    ALGORITHM: str = os.getenv("ALGORITHM", "HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "86400"))
    
    # 数据库配置
    DB_USER: str = os.getenv("DB_USER", "")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "")
    DB_HOST: str = os.getenv("DB_HOST", "")
    DB_PORT: int = int(os.getenv("DB_PORT", ""))
    DB_NAME: str = os.getenv("DB_NAME", "")
    DATABASE_URL: Optional[MySQLDsn] = None
    
    # 阿里云OSS配置
    OSS_ACCESS_KEY_ID: str = os.getenv("OSS_ACCESS_KEY_ID", "")
    OSS_ACCESS_KEY_SECRET: str = os.getenv("OSS_ACCESS_KEY_SECRET", "")
    OSS_ENDPOINT: str = os.getenv("OSS_ENDPOINT", "")
    OSS_BUCKET_NAME: str = os.getenv("OSS_BUCKET_NAME", "oss-dicom")
    OSS_URL_EXPIRE_SECONDS: int = int(os.getenv("OSS_URL_EXPIRE_SECONDS", "3600"))
    OSS_ROLE_ARN: str = os.getenv("OSS_ROLE_ARN", "")
    OSS_REGION: str = os.getenv("OSS_REGION", "")

    @field_validator("DATABASE_URL", mode="before")
    @classmethod
    def assemble_db_connection(cls, v: Optional[str], info) -> Any:
        if isinstance(v, str):
            return v
            
        values = info.data
        return MySQLDsn.build(
            scheme="mysql+pymysql",
            username=values.get("DB_USER"),
            password=values.get("DB_PASSWORD"),
            host=values.get("DB_HOST"),
            port=values.get("DB_PORT"),
            path=f"{values.get('DB_NAME')}",
        )

    class Config:
        env_file = env_file
        case_sensitive = True


settings = Settings() 

if __name__ == "__main__":
    print(f"当前环境: {settings.ENV}, 调试模式: {settings.DEBUG}")
    print(settings)
