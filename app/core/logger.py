import logging
import os
import time
from logging.handlers import RotatingFileHandler
from pathlib import Path

# 创建日志目录
LOG_DIR = Path("./logs")
LOG_DIR.mkdir(exist_ok=True)

# 定义日志格式
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

# 根据环境变量设置默认日志级别
ENV = os.getenv("ENV", "dev")
DEFAULT_LOG_LEVEL = logging.DEBUG if ENV == "dev" else logging.INFO

def setup_logger(name="meddiagsys", log_level=None):
    """
    设置并返回一个配置好的logger
    
    参数:
    - name: logger名称
    - log_level: 日志级别，如果为None则根据环境变量决定
    
    返回:
    - 配置好的logger实例
    """
    # 如果未指定日志级别，则使用默认级别
    if log_level is None:
        log_level = DEFAULT_LOG_LEVEL
        
    logger = logging.getLogger(name)
    logger.setLevel(log_level)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 控制台输出处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_formatter = logging.Formatter(LOG_FORMAT, DATE_FORMAT)
    console_handler.setFormatter(console_formatter)
    
    # 文件输出处理器 (按日期)
    today = time.strftime("%Y-%m-%d")
    log_file = os.path.join(LOG_DIR, f"{today}.log")
    file_handler = RotatingFileHandler(
        log_file, 
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding="utf-8"
    )
    file_handler.setLevel(log_level)
    file_formatter = logging.Formatter(LOG_FORMAT, DATE_FORMAT)
    file_handler.setFormatter(file_formatter)
    
    # 添加处理器到logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    
    return logger

# 创建一个默认的应用日志记录器
app_logger = setup_logger()

# 获取特定模块logger的函数
def get_logger(module_name):
    """
    获取特定模块的logger
    
    参数:
    - module_name: 模块名称
    
    返回:
    - 配置好的logger实例
    """
    return setup_logger(f"meddiagsys.{module_name}") 