from fastapi import APIRouter

from app.api.endpoints import auth, subjects, users, system_config, upload, report, diagnosis

api_router = APIRouter()
api_router.include_router(auth.router, tags=["authentication"])
api_router.include_router(subjects.router, tags=["subjects"])
api_router.include_router(users.router, tags=["system"])
api_router.include_router(system_config.router, tags=["system_config"])
api_router.include_router(upload.router, tags=["upload"])
api_router.include_router(report.router, tags=["reports"])
api_router.include_router(diagnosis.router, tags=["diagnosis"]) 