from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import date
from pathlib import Path
from app.core.logger import app_logger
import oss2
import logging
from collections import defaultdict
import asyncio
from pydantic import BaseModel

from app.db.session import get_db
from app.models.subject import SubjectInfo, SubjectCheck
from app.schemas.subject import Subject, SubjectFilter
from app.schemas.diagnosis import Diagnosis
from app.api.dependencies import get_current_user
from app.models.user import User
from app.core.config import settings
from app.models.diagnosis import DiagnosisResult

router = APIRouter()

@router.post("/subjects", response_model=List[Subject])
def filter_subjects(
    filter_params: SubjectFilter,
    region: str = Query(None, description="Filter by region"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Filter subjects by date range and optionally by region.
    Returns subject data along with diagnosis status.
    
    Frontend sends: 
    - Date filter in request body
    - Region as query parameter
    """
    app_logger.info(f"Filtering subjects with date range: {filter_params.start_date} to {filter_params.end_date}, region: {region}")
    
    # Get subjects filtered by date
    query = db.query(SubjectInfo)
    
    if filter_params.start_date:
        query = query.filter(SubjectInfo.check_date >= filter_params.start_date)
    if filter_params.end_date:
        query = query.filter(SubjectInfo.check_date <= filter_params.end_date)
    
    subjects = query.all()
    
    result = []
    for subject in subjects:
        if region:
            if region == "others":
                # 对于 others，查询除了 head 和 abdomen 以外的其他部位
                checks = db.query(SubjectCheck).filter(
                    SubjectCheck.subject_id == subject.subject_id,
                    ~SubjectCheck.region.in_(["head", "abdomen"])
                ).all()
                
                # 如果没有其他部位的检查记录，跳过这个受试者
                if not checks:
                    continue
                    
                # 返回所有其他部位的检查记录，而不仅仅是第一个
                for check in checks:
                    region_to_use = check.region
                    
                    # 获取对应部位的诊断状态
                    diagnosis = db.query(DiagnosisResult).filter(
                        DiagnosisResult.subject_id == subject.subject_id,
                        DiagnosisResult.region == region_to_use
                    ).first()
                    
                    diagnosis_status = diagnosis.status if diagnosis else "pending"
                    
                    subject_data = Subject(
                        subject_id=subject.subject_id,
                        check_date=subject.check_date,
                        name=subject.name,
                        pinyin=subject.pinyin or "",
                        age=subject.age,
                        gender=subject.gender,
                        remark=subject.remark or "",
                        region=region_to_use,  # 使用实际的区域名称
                        diagnosis_status=diagnosis_status
                    )
                    result.append(subject_data)
            else:
                check = db.query(SubjectCheck).filter(
                    SubjectCheck.subject_id == subject.subject_id,
                    SubjectCheck.region == region
                ).first()
                
                if not check:
                    continue
                
                region_to_use = region
                # Get diagnosis status if available
                diagnosis = db.query(DiagnosisResult).filter(
                    DiagnosisResult.subject_id == subject.subject_id,
                    DiagnosisResult.region == region
                ).first()
                
                diagnosis_status = diagnosis.status if diagnosis else "pending"
                
                subject_data = Subject(
                    subject_id=subject.subject_id,
                    check_date=subject.check_date,
                    name=subject.name,
                    pinyin=subject.pinyin or "",
                    age=subject.age,
                    gender=subject.gender,
                    remark=subject.remark or "",
                    region=region_to_use,  # 使用实际的区域名称
                    diagnosis_status=diagnosis_status
                )
                result.append(subject_data)
    
    app_logger.info(f"Found {len(result)} matching subjects")
    return result

@router.get("/subjects/{subject_id}", response_model=Subject)
def get_subject_by_id(
    subject_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get a single subject by ID.
    
    Frontend sends:
    - subject_id in path
    """    
    subject = db.query(SubjectInfo).filter(SubjectInfo.subject_id == subject_id).first()
    
    if not subject or subject_id == 'Unknown':
        raise HTTPException(status_code=404, detail=f"Subject with ID {subject_id} not found")
    
    # Get diagnosis status if available
    diagnosis = db.query(DiagnosisResult).filter(
        DiagnosisResult.subject_id == subject.subject_id
    ).first()
    
    diagnosis_status = diagnosis.status if diagnosis else "pending"
    
    # Create response object
    subject_data = Subject(
        subject_id=subject.subject_id,
        check_date=subject.check_date,
        name=subject.name,
        pinyin=subject.pinyin or "",
        age=subject.age,
        gender=subject.gender,
        remark=subject.remark or "",
        region='region',
        diagnosis_status=diagnosis_status
    )
    
    app_logger.info(f"Found subject with ID: {subject_id}")
    return subject_data
   

@router.get("/subjects/{subject_id}/diagnosis", response_model=Diagnosis)
def get_subject_diagnoses(
    subject_id: str,
    region: str = Query(..., description="Region to filter by (required)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get diagnosis result for a specific subject and region.
    
    Frontend sends:
    - subject_id in path
    - region as query parameter (required)
    
    Returns:
    - A dictionary with a single diagnosis result
    """
    app_logger.info(f"Fetching diagnosis result for subject ID: {subject_id}, region: {region}")
    
    # Check if subject exists
    subject = db.query(SubjectInfo).filter(SubjectInfo.subject_id == subject_id).first()
    if not subject:
        raise HTTPException(status_code=404, detail=f"Subject with ID {subject_id} not found")
    
    # Check if the region exists for this subject
    check = db.query(SubjectCheck).filter(
        SubjectCheck.subject_id == subject_id,
        SubjectCheck.region == region
    ).first()
    
    if not check:
        raise HTTPException(
            status_code=404, 
            detail=f"Subject with ID {subject_id} does not have region {region}"
        )
    
    # Get the diagnosis result for this subject and region
    diagnosis = db.query(DiagnosisResult).filter(
        DiagnosisResult.subject_id == subject_id,
        DiagnosisResult.region == region
    ).order_by(DiagnosisResult.submit_time.desc()).first()
    
    # 创建诊断数据对象
    diagnosis_data = Diagnosis(
        subject_id=subject_id,
        name=subject.name,
        pinyin=subject.pinyin or "",
        age=subject.age,
        gender=subject.gender,
        check_date=subject.check_date if not diagnosis else diagnosis.check_date,
        region=region,
        submit_time=None if not diagnosis else diagnosis.submit_time,
        submit_user=None if not diagnosis else diagnosis.submit_user,
        result="" if not diagnosis else diagnosis.result,
        status="pending" if not diagnosis else diagnosis.status
    )
    
    log_message = "Found" if diagnosis else "No"
    app_logger.info(f"{log_message} diagnosis result for subject ID: {subject_id}, region: {region}")
    return diagnosis_data

def get_region_dir(region: str) -> str:
    """
    根据传入的region返回对应的目录名
    """
    if not region:
        return "head_imgs"  # 默认目录
        
    # 转换为小写进行处理
    region = region.lower()
    return f"{region}_imgs"

@router.get("/subjects/{subject_id}/sequences/imgs")
def get_subject_sequences(
    subject_id: str,
    region: str = Query(..., description="Region to filter by (e.g., head, abdomen)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get image sequences for a specific subject and region.
    
    Frontend sends:
    - subject_id in path
    - region as query parameter (required)
    
    Returns:
    - A dictionary where keys are sequence names and values are arrays of image URLs
    - For head region, also includes video URL under the 'video' key as an array with a single URL
    """
    app_logger.info(f"Fetching image sequences for subject ID: {subject_id}, region: {region}")
    
    # Check if subject exists
    subject = db.query(SubjectInfo).filter(SubjectInfo.subject_id == subject_id).first()
    if not subject:
        raise HTTPException(status_code=404, detail=f"Subject with ID {subject_id} not found")
    
    # 获取基于region的目录
    base_dir = get_region_dir(region)
    
    # Initialize Alibaba Cloud OSS client
    auth = oss2.Auth(settings.OSS_ACCESS_KEY_ID, settings.OSS_ACCESS_KEY_SECRET)
    bucket = oss2.Bucket(auth, settings.OSS_ENDPOINT, settings.OSS_BUCKET_NAME)
    
    # List all objects in the subject's directory
    prefix = f"{base_dir}/{subject_id}/"
    app_logger.info(f"Looking for OSS objects with prefix: {prefix}")
    
    # This dictionary will store sequences as keys and lists of image URLs as values
    sequences = defaultdict(list)
    
    try:
        # 获取所有对象（处理分页）
        is_truncated = True
        next_marker = None
        all_objects = []
        
        # 循环获取所有对象，处理OSS分页
        while is_truncated:
            if next_marker:
                result = bucket.list_objects(prefix=prefix, marker=next_marker)
            else:
                result = bucket.list_objects(prefix=prefix)
                
            all_objects.extend(result.object_list)
            is_truncated = result.is_truncated
            next_marker = result.next_marker
        
        app_logger.info(f"Found {len(all_objects)} total objects with prefix: {prefix}")
        
        # 首先，收集所有序列目录
        sequence_directories = set()
        for obj in all_objects:
            # 跳过前缀本身
            if obj.key == prefix:
                continue
                
            # 提取相对路径
            relative_path = obj.key[len(prefix):]
            if not relative_path:
                continue
                
            # 查找序列名称（第一级目录）
            if '/' in relative_path:
                sequence_name = relative_path.split('/')[0]
                sequence_directories.add(sequence_name)
        
        app_logger.info(f"Detected sequence directories: {sequence_directories}")
        
        # 根据序列目录收集图像
        for seq_dir in sequence_directories:
            seq_prefix = f"{prefix}{seq_dir}/"
            
            # 获取此序列的所有JPG图像
            for obj in all_objects:
                if obj.key.startswith(seq_prefix) and obj.key.lower().endswith('.jpg'):
                    url = bucket.sign_url('GET', obj.key, settings.OSS_URL_EXPIRE_SECONDS)
                    sequences[seq_dir].append(url)
        
        # 处理根目录下的JPG（如果有）
        for obj in all_objects:
            relative_path = obj.key[len(prefix):]
            if '/' not in relative_path and obj.key.lower().endswith('.jpg'):
                url = bucket.sign_url('GET', obj.key, settings.OSS_URL_EXPIRE_SECONDS)
                sequences['default'].append(url)
        
        # 如果区域是head，还要获取视频文件
        if region == "head":
            # 视频文件路径
            video_key = f"diag_video/{subject_id}.mp4"
            
            try:
                # 检查视频是否存在
                if bucket.object_exists(video_key):
                    video_url = bucket.sign_url('GET', video_key, settings.OSS_URL_EXPIRE_SECONDS)
                    app_logger.info(f"Found video for subject {subject_id}: {video_key}")
                    # 返回视频作为一个单元素数组
                    sequences['tof3d_MRA'] = [video_url]
                else:
                    # 如果没有找到视频，设置为空数组
                    sequences['tof3d_MRA'] = []
            except Exception as e:
                app_logger.error(f"Error accessing video files: {str(e)}")
                sequences['tof3d_MRA'] = []
        
        # 移除空序列
        empty_sequences = [seq for seq, content in sequences.items() if not content]
        for seq in empty_sequences:
            del sequences[seq]
            
        # 对每个序列中的图像URL进行排序
        for seq, content in sequences.items():
            if isinstance(content, list):
                content.sort()
        
        # 记录结果
        if sequences:
            app_logger.info(f"Found {len(sequences)} sequences: {list(sequences.keys())}")
            total_images = sum(len(imgs) for seq, imgs in sequences.items())
            app_logger.info(f"Total images: {total_images}")
            if 'tof3d_MRA' in sequences and sequences['tof3d_MRA']:
                app_logger.info(f"Found video for subject {subject_id}")
        else:
            app_logger.warning(f"No image sequences found for subject {subject_id} in region {region}")
            
        return sequences
        
    except Exception as e:
        app_logger.error(f"Error accessing OSS: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving image sequences: {str(e)}")


@router.post("/subjects/sequences/counts")
async def get_sequences_counts(
    subject_ids: List[str],
    region: str = Query(..., description="Region to filter by (e.g., head, abdomen)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    批量获取多个受试者的序列数量
    
    Frontend sends:
    - subject_ids in request body
    - region as query parameter (required)
    
    Returns:
    - A dictionary with data field containing subject IDs as keys and sequence counts as values
    """
    app_logger.info(f"Fetching sequence counts for {len(subject_ids)} subjects in region: {region}")
    
    # 获取基于region的目录
    base_dir = get_region_dir(region)
    
    # 初始化阿里云OSS客户端
    auth = oss2.Auth(settings.OSS_ACCESS_KEY_ID, settings.OSS_ACCESS_KEY_SECRET)
    bucket = oss2.Bucket(auth, settings.OSS_ENDPOINT, settings.OSS_BUCKET_NAME)
    
    # 简化获取单个受试者的序列数量的函数
    async def get_sequence_count(subject_id: str) -> int:
        if not subject_id or subject_id == "Unknown":
            return 0

        prefix = f"{base_dir}/{subject_id}/"
        
        # 封装同步OSS操作以便异步执行
        def count_sequences():
            try:
                # 使用delimiter参数只列出目录，不列出文件
                folders = set()
                iterator = oss2.ObjectIterator(bucket, prefix=prefix, delimiter='/')
                for obj in iterator:
                    if obj.is_prefix():
                        folders.add(obj.key)
                
                # 对于头部区域，检查是否存在视频
                if region == "head":
                    video_key = f"diag_video/{subject_id}.mp4"
                    if bucket.object_exists(video_key):
                        folders.add("video")
                
                return len(folders)
            except Exception as e:
                app_logger.error(f"Error counting sequences for {subject_id}: {e}")
                return 0
        
        # 在事件循环的线程池中执行同步操作
        try:
            return await asyncio.get_event_loop().run_in_executor(None, count_sequences)
        except Exception as e:
            app_logger.error(f"Async error for {subject_id}: {e}")
            return 0
    
    # 创建并执行所有任务
    try:
        tasks = [get_sequence_count(subject_id) for subject_id in subject_ids]
        results = await asyncio.gather(*tasks)
        
        # 组合结果
        counts = {subject_id: count for subject_id, count in zip(subject_ids, results)}
        
        app_logger.info(f"Successfully fetched {len(counts)} sequence counts")
        return {"data": counts}
    
    except Exception as e:
        app_logger.error(f"Error in batch sequence count: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve sequence counts: {str(e)}")

@router.post("/submit_users")
async def get_submit_users(
    subject_ids: List[str],
    region: str = Query(..., description="区域名称，如head, abdomen等"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    批量获取多个受试者的诊断老师信息
    
    Frontend sends:
    - subject_ids in request body
    - region as query parameter (required)
    
    Returns:
    - A dictionary with data field containing subject IDs as keys and submit users as values
    """
    app_logger.info(f"获取{len(subject_ids)}名受试者的诊断老师信息，区域: {region}")
    
    submit_users = {}
    
    # 批量查询所有受试者的诊断结果
    for subject_id in subject_ids:
        if not subject_id or subject_id == "Unknown":
            submit_users[subject_id] = ""
            continue
            
        try:
            # 获取最新的诊断记录
            diagnosis = db.query(DiagnosisResult).filter(
                DiagnosisResult.subject_id == subject_id,
                DiagnosisResult.region == region
            ).order_by(DiagnosisResult.submit_time.desc()).first()
            
            # 如果找到诊断记录，返回提交用户名
            if diagnosis:
                submit_users[subject_id] = diagnosis.submit_user
            else:
                submit_users[subject_id] = ""
                
        except Exception as e:
            app_logger.error(f"获取受试者 {subject_id} 的诊断老师信息出错: {str(e)}")
            submit_users[subject_id] = ""
    
    app_logger.info(f"成功获取{len(submit_users)}个受试者的诊断老师信息")
    return {"data": submit_users}

