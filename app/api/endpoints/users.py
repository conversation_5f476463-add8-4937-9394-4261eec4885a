from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from app.db.session import get_db
from app.models.user import User
from app.schemas.user import UserCreate, User as UserSchema
from app.api.dependencies import get_current_user
from app.core.security import get_password_hash

router = APIRouter()


class UserCreateRequest(BaseModel):
    username: str
    password: str
    name: str
    role: str = "user"


class UserUpdateStatusRequest(BaseModel):
    active: bool


class UserResponse(BaseModel):
    username: str
    name: str
    role: str
    is_disabled: bool
    
    class Config:
        from_attributes = True


@router.get("/system/users", response_model=List[UserResponse])
def get_users(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[UserResponse]:
    """
    获取所有用户列表
    需要管理员权限
    """
    # 检查用户是否有管理员权限
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作"
        )
    
    users = db.query(User).all()
    return users


@router.post("/system/users", response_model=UserResponse)
def create_user(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    user_data: UserCreateRequest
) -> UserResponse:
    """
    创建新用户
    需要管理员权限
    """
    # 检查用户是否有管理员权限
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作"
        )
    
    # 检查用户名是否已存在
    existing_user = db.query(User).filter(User.username == user_data.username).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 创建新用户对象
    new_user = User(
        username=user_data.username,
        password=get_password_hash(user_data.password),
        name=user_data.name,
        is_disabled=False,  # 默认激活状态
        role=user_data.role
    )
    
    # 保存到数据库
    db.add(new_user)
    db.commit()
    db.refresh(new_user)
    
    return new_user


@router.patch("/system/users/{username}/status", response_model=UserResponse)
def update_user_status(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    username: str,
    status_data: UserUpdateStatusRequest
) -> UserResponse:
    """
    更新用户状态（启用/禁用）
    需要管理员权限
    """
    # 检查用户是否有管理员权限
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作"
        )
    
    # 查找用户
    user = db.query(User).filter(User.username == username).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 不允许禁用自己的账户
    if username == current_user.username:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能修改自己的账户状态"
        )
    
    # 更新用户状态
    user.is_disabled = not status_data.active
    db.commit()
    db.refresh(user)
    
    return user


@router.put("/system/users/{username}", response_model=UserResponse)
def update_user(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    username: str,
    user_data: UserCreateRequest
) -> UserResponse:
    """
    更新用户信息
    需要管理员权限
    """
    # 检查用户是否有管理员权限
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作"
        )
    
    # 查找用户
    user = db.query(User).filter(User.username == username).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 更新用户信息
    user.name = user_data.name
    user.role = user_data.role
    
    # 如果提供了新密码，则更新密码
    if user_data.password:
        user.password = get_password_hash(user_data.password)
    
    db.commit()
    db.refresh(user)
    
    return user 