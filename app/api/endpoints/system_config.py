from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Dict, Any
import logging
import datetime

from app.api.dependencies import get_db, get_current_user
from app.models.user import User
from app.models.system_config import SystemConfig
from app.schemas.system_config import SystemConfigRequest, SystemConfigResponse, DateRangeRequest

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/system/config/{key}", response_model=SystemConfigResponse)
def update_system_config(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    key: str,
    config_data: SystemConfigRequest
):
    """
    Update system configuration by key
    Requires admin access
    """
    # Check if user has admin role
    if current_user.role != "admin":
        logger.warning(f"User {current_user.username} attempted to update system config without admin rights")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作"
        )
    
    try:
        # Get existing config or create new one
        config = db.query(SystemConfig).filter(SystemConfig.key == key).first()
        if not config:
            config = SystemConfig(
                key=key,
                value=config_data.value,
                type="json",
                description=f"System configuration for {key}"
            )
            db.add(config)
            logger.info(f"User {current_user.username} created new config: {key}")
        else:
            config.value = config_data.value
            logger.info(f"User {current_user.username} updated config: {key}")
        
        db.commit()
        db.refresh(config)
        return config
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating system config: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存配置时发生错误: {str(e)}"
        )


@router.get("/system/config/{key}", response_model=SystemConfigResponse)
def get_system_config(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    key: str
):
    """
    Get system configuration by key
    """
    try:
        config = db.query(SystemConfig).filter(SystemConfig.key == key).first()
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到配置: {key}"
            )
        return config
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving system config: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配置时发生错误: {str(e)}"
        )


@router.post("/system/settings/date_range", response_model=SystemConfigResponse)
def update_date_range(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    date_range: DateRangeRequest
):
    """
    Update date range configuration
    Requires admin access
    """
    # Check if user has admin role
    if current_user.role != "admin":
        logger.warning(f"User {current_user.username} attempted to update date range without admin rights")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作"
        )
    
    try:
        # Get existing config or create new one
        config = db.query(SystemConfig).filter(SystemConfig.key == "date_range").first()
        if not config:
            config = SystemConfig(
                key="date_range",
                value={"start_date": date_range.start_date, "end_date": date_range.end_date},
                type="json",
                description="系统日期范围设置"
            )
            db.add(config)
            logger.info(f"User {current_user.username} created date range config: {date_range.start_date} to {date_range.end_date}")
        else:
            config.value = {"start_date": date_range.start_date, "end_date": date_range.end_date}
            logger.info(f"User {current_user.username} updated date range config: {date_range.start_date} to {date_range.end_date}")
        
        db.commit()
        db.refresh(config)
        return config
    except Exception as e:
        print(repr(e))
        db.rollback()
        logger.error(f"Error updating date range: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"保存日期范围设置时发生错误: {str(e)}"
        )


@router.get("/system/settings/date_range", response_model=SystemConfigResponse)
def get_date_range(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get date range configuration
    """
    try:
        config = db.query(SystemConfig).filter(SystemConfig.key == "date_range").first()
        config.value['end_date'] = datetime.date.today().strftime("%Y-%m-%d")
        if not config:
            # Return default empty configuration
            return SystemConfig(
                key="date_range",
                value={"start_date": "", "end_date": ""},
                type="json",
                description="系统日期范围设置"
            )
        return config
    except Exception as e:
        logger.error(f"Error retrieving date range: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取日期范围设置时发生错误: {str(e)}"
        )


@router.get("/system/config", response_model=List[SystemConfigResponse])
def get_all_system_configs(
    *,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get all system configurations
    Requires admin access
    """
    # Check if user has admin role
    if current_user.role != "admin":
        logger.warning(f"User {current_user.username} attempted to get all system configs without admin rights")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有足够的权限执行此操作"
        )
    
    try:
        configs = db.query(SystemConfig).all()
        return configs
    except Exception as e:
        logger.error(f"Error retrieving all system configs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取所有系统配置时发生错误: {str(e)}"
        ) 