from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime, date

from app.db.session import get_db
from app.models.diagnosis import DiagnosisResult
from app.models.user import User
from app.models.subject import SubjectInfo
from app.schemas.diagnosis import Diagnosis, DiagnosisCreate
from app.api.dependencies import get_current_user
from app.core.logger import app_logger

router = APIRouter()

@router.post("/diagnosis/save", response_model=Diagnosis)
def save_diagnosis(
    diagnosis_data: DiagnosisCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    保存诊断结果
    
    支持暂存(draft)和提交(submitted)两种模式
    """
    app_logger.info(f"保存诊断结果: {diagnosis_data.subject_id}, {diagnosis_data.region}, 状态:{diagnosis_data.status}")
    
    # 验证用户
    if not current_user or current_user.username != diagnosis_data.username:
        raise HTTPException(status_code=403, detail="无权限保存诊断结果")
    
    # 确认患者存在
    subject = db.query(SubjectInfo).filter(SubjectInfo.subject_id == diagnosis_data.subject_id).first()
    if not subject:
        raise HTTPException(status_code=404, detail=f"患者ID {diagnosis_data.subject_id} 不存在")
    
    # 查找已有诊断结果
    existing = db.query(DiagnosisResult).filter(
        DiagnosisResult.subject_id == diagnosis_data.subject_id,
        DiagnosisResult.region == diagnosis_data.region
    ).first()
    
    # 准备数据
    current_time = datetime.now()
    
    # 创建或更新诊断结果
    if existing:
        # 检查是否可以修改
        if existing.status == "submitted" and diagnosis_data.status == "submitted":
            raise HTTPException(status_code=400, detail="该诊断结果已提交，不可再修改")
        
        # 更新现有诊断
        existing.result = diagnosis_data.result
        existing.submit_time = current_time
        existing.submit_user = diagnosis_data.username
        existing.status = diagnosis_data.status
        
        db.commit()
        db.refresh(existing)
        diagnosis = existing
    else:
        # 创建新诊断
        new_diagnosis = DiagnosisResult(
            subject_id=diagnosis_data.subject_id,
            name=subject.name,
            check_date=diagnosis_data.check_date if isinstance(diagnosis_data.check_date, date) 
                      else date.fromisoformat(diagnosis_data.check_date),
            region=diagnosis_data.region,
            submit_time=current_time,
            submit_user=diagnosis_data.username,
            result=diagnosis_data.result,
            status=diagnosis_data.status
        )
        db.add(new_diagnosis)
        db.commit()
        db.refresh(new_diagnosis)
        diagnosis = new_diagnosis
    
    # 返回结果
    return Diagnosis(
        id=diagnosis.id,
        subject_id=diagnosis.subject_id,
        # name=subject.name,
        pinyin=subject.pinyin,
        age=subject.age,
        gender=subject.gender,
        check_date=diagnosis.check_date,
        region=diagnosis.region,
        submit_time=diagnosis.submit_time,
        submit_user=diagnosis.submit_user,
        result=diagnosis.result,
        status=diagnosis.status,
        remark=None
    )
