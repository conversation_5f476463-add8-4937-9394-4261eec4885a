from fastapi import APIRouter, UploadFile, File, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime
import pandas as pd
import io
import logging
from typing import List
import oss2
from aliyunsdkcore import client
from aliyunsdksts.request.v20150401 import AssumeRoleRequest
import json

from app.db.session import get_db
from app.models.subject import SubjectInfo, SubjectCheck
from app.api.dependencies import get_current_user
from app.core.config import settings

router = APIRouter()
logger = logging.getLogger(__name__)

def get_sts_token():
    """
    获取阿里云STS临时访问凭证
    """
    try:
        clt = client.AcsClient(
            settings.OSS_ACCESS_KEY_ID,
            settings.OSS_ACCESS_KEY_SECRET,
        )
        
        request = AssumeRoleRequest.AssumeRoleRequest()
        request.set_accept_format('json')
        request.set_RoleArn(settings.OSS_ROLE_ARN)
        request.set_RoleSessionName('upload_session')
        request.set_DurationSeconds(3600)  # 1小时有效期
        
        response = clt.do_action_with_exception(request)
        token_info = json.loads(response)
        
        return {
            'accessKeyId': token_info['Credentials']['AccessKeyId'],
            'accessKeySecret': token_info['Credentials']['AccessKeySecret'],
            'securityToken': token_info['Credentials']['SecurityToken'],
            'expiration': token_info['Credentials']['Expiration'],
            'region': settings.OSS_REGION,
            'bucket': settings.OSS_BUCKET_NAME,
            'endpoint': settings.OSS_ENDPOINT
        }
    except Exception as e:
        logger.error(f"获取STS Token失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取上传凭证失败")

@router.post("/upload/excel")
async def upload_excel(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    上传并处理Excel文件，提取受试者信息并存入数据库
    """
    if not file.filename.endswith(('.xlsx')):
        raise HTTPException(status_code=400, detail="请上传Excel文件(.xls或.xlsx格式)")
    
    try:
        # 读取Excel文件内容
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))
        
        if df.empty:
            raise HTTPException(status_code=400, detail="Excel文件不包含任何数据")
        
        # 验证必要的列是否存在
        required_columns = ["姓名", "PID", "拼音", "扫描日期", "年龄", "性别", "诊断部位"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise HTTPException(
                status_code=400,
                detail=f"Excel文件缺少必要的列: {', '.join(missing_columns)}"
            )
        
        # 处理数据
        results = []
        processed_subjects = {}  # 用于去重患者信息
        error_rows = []
        
        for index, row in df.iterrows():
            try:
                # 提取并验证基本数据
                pid = str(row["PID"]).strip()
                name = str(row["姓名"]).strip()
                pinyin = str(row["拼音"]).strip()
                scan_date_str = str(row["扫描日期"]).strip()
                age_str = str(row["年龄"]).replace('Y', '').strip()
                gender = str(row["性别"]).strip()
                check_part = str(row["诊断部位"]).strip().lower()
                
                # 处理日期格式 (20250101 -> 2025-01-01)
                try:
                    scan_date = datetime.strptime(scan_date_str, "%Y%m%d").date()
                except ValueError:
                    error_rows.append(f"第{index+2}行: 扫描日期格式错误，应为YYYYMMDD格式")
                    continue
                
                # 性别验证
                if gender not in ['M', 'F']:
                    error_rows.append(f"第{index+2}行: 性别必须为M或F")
                    continue
                
                # 如果这个患者还没处理过，添加到主表
                if pid not in processed_subjects:
                    # 检查是否已存在该患者
                    existing_subject = db.query(SubjectInfo).filter(SubjectInfo.subject_id == pid).first()
                    
                    if not existing_subject:
                        subject_info = SubjectInfo(
                            subject_id=pid,
                            name=name,
                            pinyin=pinyin,
                            check_date=scan_date,
                            age=age_str,
                            gender=gender
                        )
                        db.add(subject_info)
                        db.flush()
                    
                    processed_subjects[pid] = True
                    results.append({"pid": pid, "name": name})
                
                # 添加检查部位记录
                subject_check = SubjectCheck(
                    subject_id=pid,
                    region=check_part
                )
                db.add(subject_check)
            
            except Exception as row_error:
                error_rows.append(f"第{index+2}行: 处理出错 - {str(row_error)}")
                logger.error(f"处理Excel第{index+2}行时出错: {str(row_error)}")
        
        if not results and error_rows:
            # 如果全部行都处理失败，回滚并报错
            db.rollback()
            raise HTTPException(status_code=400, detail=f"Excel处理失败: {'; '.join(error_rows[:5])}")
        
        db.commit()
        
        # 返回处理结果，包括成功和错误信息
        response = {
            "status": "success",
            "message": f"成功导入{len(results)}位患者信息",
            "data": results
        }
        
        if error_rows:
            response["warnings"] = error_rows
        
        return response
    
    except HTTPException:
        # 直接传递HTTP异常
        raise
    except Exception as e:
        db.rollback()
        logger.exception("处理Excel文件时发生错误")
        raise HTTPException(status_code=500, detail=f"处理Excel文件时发生错误: {str(e)}")

@router.post("/upload/sts-token")
async def get_upload_token(
    current_user = Depends(get_current_user)
):
    """
    获取OSS上传的STS Token
    """
    try:
        token_info = get_sts_token()
        return {
            "status": "success",
            "data": token_info
        }
    except Exception as e:
        logger.error(f"获取上传Token失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取上传凭证失败")

