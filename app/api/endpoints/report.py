from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional, Dict, Any
from datetime import date
from fastapi.responses import FileResponse
import tempfile
import os
import pathlib
from fastapi.background import BackgroundTasks
import zipfile
import io
from fastapi.responses import StreamingResponse
import urllib.parse

from app.api.dependencies import get_db, get_current_user
from app.models.subject import SubjectInfo, SubjectCheck
from app.models.diagnosis import DiagnosisResult
from app.models.user import User
from app.schemas.report import (
    ReportResponse, Report, ReportParams,
    BatchDownloadRequest, StatsResponse, ReportContext
)
from app.core.logger import get_logger
from app.core.config import settings
from app.utils.pdf_generator import create_report_pdf

router = APIRouter()

@router.get("/reports", response_model=ReportResponse)
async def get_reports(
    params: ReportParams = Depends(),
    db: Session = Depends(get_db)
):
    """
    获取指定日期范围内的报告列表
    - 从受试者信息表中获取基本信息
    - 通过诊断结果表判断检查状态
    - 只返回subject_id包含"PID"字符的报告
    """
    # 获取日志记录器
    logger = get_logger("reports")
    
    # 查询日期范围内的受试者信息
    query = db.query(
        SubjectInfo.subject_id, 
        SubjectInfo.name,
        SubjectInfo.check_date
    ).filter(
        SubjectInfo.check_date >= params.start_date,
        SubjectInfo.check_date <= params.end_date
    )
    
    subjects_data = query.all()
    
    reports = []
    for subject in subjects_data:
        # 过滤掉不包含"PID"的subject_id
        if "PID" not in subject.subject_id:
            logger.debug(f"跳过不包含PID的受试者ID: {subject.subject_id}")
            continue
            
        # 获取该受试者的所有检查部位
        regions = db.query(SubjectCheck.region) \
            .filter(SubjectCheck.subject_id == subject.subject_id) \
            .all()
        
        regions_list = [r.region for r in regions]
        
        if not regions_list:
            continue
            
        # 查询该受试者各部位的诊断状态
        diagnosis_results = db.query(DiagnosisResult.region) \
            .filter(
                DiagnosisResult.subject_id == subject.subject_id,
                DiagnosisResult.status == "submitted"  # 只计算已提交的诊断
            ).all()
            
        diagnosed_regions = [r.region for r in diagnosis_results]
        
        # 判断诊断状态
        status = 1  # 默认为完成
        status_text = "诊断完成"
        
        # 检查是否有未诊断的部位
        undiagnosed_regions = [r for r in regions_list if r not in diagnosed_regions]
        if undiagnosed_regions:
            status = 0
            status_text = f"{', '.join(undiagnosed_regions)}部位未诊断"
        
        # 构建报告对象
        report = Report(
            subject_id=subject.subject_id,
            name=subject.name,
            regions=regions_list,
            status=status,
            status_text=status_text,
            check_date=subject.check_date.isoformat()
        )
        reports.append(report)
    
    logger.info(f"共筛选出 {len(reports)} 条包含PID的报告记录")
    return ReportResponse(reports=reports)


@router.get("/reports/{subject_id}/download")
async def download_report(
    subject_id: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    生成并下载指定受试者的诊断报告PDF文件
    
    返回:
        - FileResponse: 生成的PDF报告文件
    """
    # 获取日志记录器
    logger = get_logger("reports")
    
    logger.info(f"用户 {current_user.username} 尝试下载报告 {subject_id}")
    
    # 检查subject_id是否包含PID
    if "PID" not in subject_id:
        logger.warning(f"受试者ID不包含PID: {subject_id}")
        raise HTTPException(status_code=400, detail="无效的受试者ID，必须包含PID")
    
    # 获取受试者基本信息
    subject_info = db.query(
        SubjectInfo
    ).filter(
        SubjectInfo.subject_id == subject_id
    ).first()
    
    if not subject_info:
        logger.warning(f"未找到受试者信息: {subject_id}")
        raise HTTPException(status_code=404, detail="未找到受试者信息")
    
    # 获取受试者诊断结果
    diagnosis_results = db.query(
        DiagnosisResult.region,
        DiagnosisResult.result
    ).filter(
        DiagnosisResult.subject_id == subject_id,
        DiagnosisResult.status == "submitted"  # 只包含已提交的诊断
    ).all()
    
    if not diagnosis_results:
        logger.warning(f"受试者 {subject_id} 未找到诊断结果或诊断尚未完成")
        raise HTTPException(status_code=404, detail="未找到诊断结果或诊断尚未完成")
    
    # 创建报告上下文对象
    context = ReportContext(
        subject_id=subject_id,
        name=subject_info.name,
        gender="男" if subject_info.gender == "M" else "女",
        age=subject_info.age,
        check_date=subject_info.check_date,
        diagnosis_results=diagnosis_results
    )
    
    # 生成临时文件
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
    temp_file.close()
    
    # 创建PDF
    try:
        create_report_pdf(temp_file.name, context)
        
        # 添加清理任务到后台任务
        def cleanup_temp_file():
            if os.path.exists(temp_file.name):
                os.unlink(temp_file.name)
        
        background_tasks.add_task(cleanup_temp_file)
        
        logger.info(f"用户 {current_user.username} 成功下载报告 {subject_id}")
        
        # 处理文件名，对中文进行编码
        filename = f"{context.name}_{context.check_date.strftime('%Y-%m-%d')}_检查报告.pdf"
        encoded_filename = urllib.parse.quote(filename)
        
        # 返回PDF文件
        return FileResponse(
            path=temp_file.name,
            filename=filename,
            headers={"Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"},
            media_type="application/pdf"
        )
    except Exception as e:
        # 发生错误时删除临时文件
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)
        
        logger.exception(f"用户 {current_user.username} 下载报告 {subject_id} 失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成报告失败: {str(e)}")

@router.get("/stats", response_model=StatsResponse)
async def get_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
):
    """
    获取诊断统计数据
    - 总患者数
    - 已诊断数量和总诊断数量
    - 各部位待诊断数量
    """
    # 获取日志记录器
    logger = get_logger("stats")
    logger.info(f"用户 {current_user.username} 请求获取统计数据")
    
    # 构建日期筛选条件
    date_filter_conditions = []
    if start_date:
        date_filter_conditions.append(SubjectInfo.check_date >= start_date)
    if end_date:
        date_filter_conditions.append(SubjectInfo.check_date <= end_date)
    
    # 获取总患者数（只包含PID患者）
    patients_query = db.query(SubjectInfo.subject_id).filter(
        SubjectInfo.subject_id.like("%PID%")
    )
    
    if date_filter_conditions:
        patients_query = patients_query.filter(*date_filter_conditions)
    
    total_patients = patients_query.distinct().count()
    
    # 获取所有需要诊断的部位记录
    total_checks_query = db.query(SubjectCheck).join(
        SubjectInfo, 
        SubjectInfo.subject_id == SubjectCheck.subject_id
    ).filter(
        SubjectInfo.subject_id.like("%PID%")
    )
    
    if date_filter_conditions:
        total_checks_query = total_checks_query.filter(*date_filter_conditions)
    
    total_checks = total_checks_query.all()
    
    # 统计总诊断数量
    total_diagnosis_count = len(total_checks)
    
    # 获取已完成诊断的记录
    diagnosed_query = db.query(DiagnosisResult).join(
        SubjectInfo,
        SubjectInfo.subject_id == DiagnosisResult.subject_id
    ).filter(
        SubjectInfo.subject_id.like("%PID%"),
        DiagnosisResult.status == "submitted"
    )
    
    if date_filter_conditions:
        diagnosed_query = diagnosed_query.filter(*date_filter_conditions)
    
    diagnosed_results = diagnosed_query.all()
    
    # 计算已诊断数量
    diagnosed_count = len(diagnosed_results)
    
    # 按部位统计待诊断数量
    region_counts = {}
    
    # 获取所有检查部位及其数量
    regions_dict = {}
    for check in total_checks:
        if check.region in regions_dict:
            regions_dict[check.region] += 1
        else:
            regions_dict[check.region] = 1
    
    # 获取已诊断部位及其数量
    diagnosed_dict = {}
    for result in diagnosed_results:
        if result.region in diagnosed_dict:
            diagnosed_dict[result.region] += 1
        else:
            diagnosed_dict[result.region] = 1
    
    # 计算各部位待诊断数量
    for region, count in regions_dict.items():
        diagnosed = diagnosed_dict.get(region, 0)
        if count - diagnosed > 0:  # 只返回有待诊断的部位
            region_counts[region] = count - diagnosed
    
    
    # 将结果整合为响应格式
    stats_response = StatsResponse(
        totalPatients=total_patients,
        diagnosedCount=diagnosed_count,
        totalDiagnosisCount=total_diagnosis_count,
        regionCounts=region_counts
    )
    
    logger.info(f"成功获取统计数据: 共 {total_patients} 名患者, {diagnosed_count}/{total_diagnosis_count} 已诊断")
    return stats_response


@router.post("/reports/batch-download")
async def batch_download_reports(
    request: BatchDownloadRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    批量生成并下载指定受试者的诊断报告PDF文件，打包成ZIP文件返回
    
    返回:
        - StreamingResponse: 包含多个PDF报告的ZIP文件
    """
    # 获取日志记录器
    logger = get_logger("reports")
    
    logger.info(f"用户 {current_user.username} 尝试批量下载 {len(request.subject_ids)} 份报告")
    
    if not request.subject_ids:
        raise HTTPException(status_code=400, detail="未提供受试者ID列表")
    
    # 验证所有ID都包含PID
    invalid_ids = [sid for sid in request.subject_ids if "PID" not in sid]
    if invalid_ids:
        raise HTTPException(status_code=400, detail="部分受试者ID无效，必须包含PID")
    
    # 创建内存中的ZIP文件
    zip_buffer = io.BytesIO()
    
    # 用于存储临时文件列表，以便后续清理
    temp_files = []
    
    try:
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for subject_id in request.subject_ids:
                # 获取受试者基本信息
                subject_info = db.query(
                    SubjectInfo
                ).filter(
                    SubjectInfo.subject_id == subject_id
                ).first()
                
                if not subject_info:
                    logger.warning(f"未找到受试者信息: {subject_id}")
                    continue
                
                # 获取受试者诊断结果
                diagnosis_results = db.query(
                    DiagnosisResult.region,
                    DiagnosisResult.result
                ).filter(
                    DiagnosisResult.subject_id == subject_id,
                    DiagnosisResult.status == "submitted"  # 只包含已提交的诊断
                ).all()
                
                if not diagnosis_results:
                    logger.warning(f"受试者 {subject_id} 未找到诊断结果或诊断尚未完成")
                    continue
                
                # 创建报告上下文对象
                context = ReportContext(
                    subject_id=subject_id,
                    name=subject_info.name,
                    gender="男" if subject_info.gender == "M" else "女",
                    age=subject_info.age,
                    check_date=subject_info.check_date,
                    diagnosis_results=diagnosis_results
                )
                
                # 生成临时文件
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
                temp_file.close()
                temp_files.append(temp_file.name)
                
                # 创建PDF
                create_report_pdf(temp_file.name, context)
                
                # 生成ZIP中的文件名 - 使用zipfile的UTF-8支持
                filename = f"{context.name}_{context.check_date.strftime('%Y-%m-%d')}_检查报告.pdf"
                
                # 添加到ZIP文件 - 指定UTF-8编码
                zip_info = zipfile.ZipInfo(filename)
                zip_info.flag_bits |= 0x800  # 设置UTF-8标志
                
                with open(temp_file.name, 'rb') as pdf_file:
                    pdf_content = pdf_file.read()
                    zip_file.writestr(zip_info, pdf_content)
                
            logger.info(f"用户 {current_user.username} 成功批量生成 {len(temp_files)} 份报告")
        
        # 将ZIP缓冲区指针重置到开始位置
        zip_buffer.seek(0)
        
        # 添加清理任务到后台任务
        def cleanup_temp_files():
            for file_path in temp_files:
                if os.path.exists(file_path):
                    os.unlink(file_path)
        
        background_tasks.add_task(cleanup_temp_files)
        
        # 生成文件名并进行URL编码
        current_date = date.today().strftime('%Y%m%d')
        filename = f"批量检查报告_{current_date}.zip"
        encoded_filename = urllib.parse.quote(filename)
        
        # 返回ZIP文件
        return StreamingResponse(
            zip_buffer,
            media_type="application/zip",
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}"
            }
        )
    except Exception as e:
        # 发生错误时删除临时文件
        for file_path in temp_files:
            if os.path.exists(file_path):
                os.unlink(file_path)
        
        logger.error(f"用户 {current_user.username} 批量下载报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量生成报告失败: {str(e)}")
