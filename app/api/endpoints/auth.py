from datetime import timedelta
from typing import Annotated, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRe<PERSON>Form
from sqlalchemy.orm import Session

from app.api.dependencies import authenticate_user, get_current_user
from app.core.config import settings
from app.core.security import create_access_token
from app.db.session import get_db
from app.schemas.token import Token
from app.schemas.user import UserLogin
from app.models.user import User

router = APIRouter()

@router.post("/login", response_model=Dict[str, Any])
def login_access_token(
    user_login: UserLogin,
    db: Annotated[Session, Depends(get_db)]
) -> Dict[str, Any]:
    """
    用户登录接口，接收用户名和密码，返回用户信息和令牌
    """
    user = authenticate_user(db, user_login.username, user_login.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码不正确",
        )
    
    # 为通过验证的用户创建访问令牌
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        user.username, expires_delta=access_token_expires
    )
    
    # 返回符合前端期望的格式
    return {
        "user": {
            "username": user.username,
            "name": user.name
        },
        "token": access_token
    }

@router.get("/auth/check")
def check_authentication(current_user: User = Depends(get_current_user)):
    """
    验证用户的身份认证状态
    此端点需要有效的访问令牌
    如果令牌有效，将返回用户信息，否则将返回401错误
    """
    return {
        "authenticated": True,
        "user": {
            "username": current_user.username,
            "name": current_user.name
        }
    } 