services:
  app:
    build: . # 表示使用当前目录下的 Dockerfile 来构建镜像
    container_name: meddiagsys
    restart: always
    environment:
      - TZ=CST
      - ENV=prod
    ports:
      - "8080:8080" 
    depends_on:
      mysql_db: # 声明 app 服务依赖于 mysql_db 服务
        condition: service_healthy # 等待 mysql_db 服务健康后再启动 app 服务

  # MySQL 服务
  mysql_db:
    image: mysql:8.0 # 或者 mysql:latest，或你需要的特定版本
    container_name: mysql_db
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=mri6393
      - MYSQL_DATABASE=meddiagsys                     # 创建名为 mydatabase 的数据库
      - MYSQL_USER=mri                           # 创建名为 myuser 的用户
      - MYSQL_PASSWORD=rn4jPWKthyyTmy2r                   # myuser 用户的密码
    ports:
      - "3303:3306" 
    volumes:
      - mysql_data:/var/lib/mysql # 将 MySQL 数据持久化到名为 mysql_data 的 Docker 卷中
                                  # 这样即使容器被删除，数据也不会丢失
    healthcheck: # 健康检查，确保 mysql 服务真正可用
      test: ["CMD-SHELL", "mysqladmin ping -h localhost -u root -p$${MYSQL_ROOT_PASSWORD}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s # 给MySQL足够的时间启动

volumes:
  mysql_data: # 定义一个命名的卷，用于持久化 MySQL 数据
