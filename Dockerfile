# Dockerfile

# 1. 选择一个合适的 Python 基础镜像
FROM python:3.11 AS base

# 设置推荐的环境变量
ENV PYTHONFAULTHANDLER=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100

# 3. 创建并设置工作目录
WORKDIR /app

# 4. 复制依赖定义文件
COPY pyproject.toml .
COPY uv.lock .

# 5. 使用 uv 安装依赖
RUN pip install uv -i https://pypi.tuna.tsinghua.edu.cn/simple
RUN uv venv
RUN uv sync

# 6. 复制项目源代码到工作目录
COPY . .

# 7. 暴露应用程序运行的端口
EXPOSE 8080

# 8. 定义容器启动时运行的命令
CMD ["uv", "run", "gunicorn", "app.main:app", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8080"]
