#!/usr/bin/env python
"""
运行脚本，用于在不同环境下启动应用
用法:
    python run.py [dev|prod]
"""

import os
import sys
import subprocess
import uvicorn
import os
from app.core.config import settings

def run_dev():
    """开发环境运行"""
    os.environ["ENV"] = "dev"
    print("🚀 以开发环境启动应用...")
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8080,
        reload=settings.DEBUG
    )


def run_prod():
    """生产环境运行"""
    os.environ["ENV"] = "prod"
    print("🚀 以生产环境启动应用...")
    
    # 使用docker-compose启动
    subprocess.run(["docker-compose", "up", "-d", "--build"])
    print("✅ 容器已启动，可通过 docker-compose logs -f 查看日志")


if __name__ == "__main__":
    # 默认为开发环境
    env = "dev"
    
    # 从命令行参数获取环境
    if len(sys.argv) > 1:
        env = sys.argv[1].lower()
    
    if env == "prod":
        run_prod()
    else:
        run_dev() 