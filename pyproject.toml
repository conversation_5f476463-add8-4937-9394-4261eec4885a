[project]
name = "meddiagsys"
version = "0.1.0"
description = "医学影像诊断系统后端"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.106.0",
    "uvicorn>=0.25.0",
    "sqlalchemy>=2.0.25",
    "pymysql>=1.1.0",
    "cryptography>=42.0.0",
    "python-jose>=3.3.0",
    "passlib>=1.7.4",
    "python-multipart>=0.0.6",
    "pydantic>=2.5.3",
    "bcrypt>=4.0.1",
    "python-dotenv>=1.0.0",
    "alembic>=1.13.1",
    "pydantic-settings>=2.8.1",
    "oss2>=2.19.1",
    "pypinyin>=0.54.0",
    "pandas>=2.2.3",
    "openpyxl>=3.1.5",
    "aliyun-python-sdk-sts>=3.1.2",
    "fpdf>=1.7.2",
    "gunicorn>=23.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "black>=23.12.0",
    "isort>=5.13.2",
    "mypy>=1.7.1"
]

[tool.black]
line-length = 88
target-version = ["py311"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple/"
default = true
